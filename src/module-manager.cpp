#include "module-manager.h"
#include "display.h"

ModuleManager::ModuleManager(Display *display) : _display(display)
{
}

ModuleManager::~ModuleManager()
{
    stopAllModules();

    // Clean up display regions
    for (DisplayRegion *region : _regions)
    {
        if (region)
        {
            _display->releaseRegion(region);
        }
    }
    _regions.clear();

    // Clean up modules (but don't delete them - they're owned by the caller)
    _modules.clear();
}

bool ModuleManager::registerModule(Module *module, int32_t x, int32_t y, int32_t width, int32_t height)
{
    if (!module || !_display)
    {
        Serial.println("ModuleManager: Cannot register module - null pointer");
        return false;
    }

    // Check if module is already registered
    for (Module *existingModule : _modules)
    {
        if (existingModule == module)
        {
            Serial.printf("ModuleManager: Module %s already registered\n", module->getName());
            return false;
        }
    }

    // Create display region for the module
    DisplayRegion *region = _display->createRegion(x, y, width, height);
    if (!region)
    {
        Serial.printf("ModuleManager: Failed to create display region for module %s\n", module->getName());
        return false;
    }

    // Initialize the module with the region
    if (!module->initialize(region))
    {
        Serial.printf("ModuleManager: Failed to initialize module %s\n", module->getName());
        _display->releaseRegion(region);
        return false;
    }

    // Add to our lists
    _modules.push_back(module);
    _regions.push_back(region);

    Serial.printf("ModuleManager: Registered module %s at (%d,%d,%d,%d)\n",
                  module->getName(), x, y, width, height);
    return true;
}

bool ModuleManager::startAllModules()
{
    bool allStarted = true;

    Serial.printf("ModuleManager: Starting %d modules...\n", _modules.size());

    for (Module *module : _modules)
    {
        if (!module->start())
        {
            Serial.printf("ModuleManager: Failed to start module %s\n", module->getName());
            allStarted = false;
        }
    }

    if (allStarted)
    {
        Serial.println("ModuleManager: All modules started successfully");
    }
    else
    {
        Serial.println("ModuleManager: Some modules failed to start");
    }

    return allStarted;
}

void ModuleManager::stopAllModules()
{
    Serial.printf("ModuleManager: Stopping %d modules...\n", _modules.size());

    for (Module *module : _modules)
    {
        module->stop();
    }

    Serial.println("ModuleManager: All modules stopped");
}

bool ModuleManager::sendTouchEvent(ModuleEventType type, int32_t x, int32_t y)
{
    Module *targetModule = findModuleAtPoint(x, y);
    if (!targetModule)
    {
        return false;
    }

    // Convert global coordinates to module-local coordinates
    int32_t localX = x - targetModule->getX();
    int32_t localY = y - targetModule->getY();

    ModuleEvent event(type, localX, localY);
    return targetModule->sendEvent(event);
}

bool ModuleManager::sendButtonEvent(ModuleEventType type, uint8_t buttonId)
{
    ModuleEvent event(type, 0, 0, buttonId);
    return broadcastEvent(event) > 0;
}

bool ModuleManager::sendEventToModule(const char *moduleName, const ModuleEvent &event)
{
    Module *module = getModule(moduleName);
    if (!module)
    {
        return false;
    }

    return module->sendEvent(event);
}

uint32_t ModuleManager::broadcastEvent(const ModuleEvent &event)
{
    uint32_t count = 0;

    for (Module *module : _modules)
    {
        if (module->sendEvent(event))
        {
            count++;
        }
    }

    return count;
}

Module *ModuleManager::getModule(const char *name)
{
    for (Module *module : _modules)
    {
        if (strcmp(module->getName(), name) == 0)
        {
            return module;
        }
    }
    return nullptr;
}

void ModuleManager::printStatus() const
{
    Serial.printf("ModuleManager: Status of %d modules:\n", _modules.size());

    for (const Module *module : _modules)
    {
        Serial.printf("  %s: %s at (%d,%d,%d,%d)\n",
                      module->getName(),
                      module->isRunning() ? "RUNNING" : "STOPPED",
                      module->getX(), module->getY(),
                      module->getWidth(), module->getHeight());
    }
}

Module *ModuleManager::findModuleAtPoint(int32_t x, int32_t y)
{
    // Find the first module that contains this point
    // Note: If modules overlap, this returns the first one found
    for (Module *module : _modules)
    {
        if (module->containsPoint(x, y))
        {
            return module;
        }
    }
    return nullptr;
}
