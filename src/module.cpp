#include "module.h"
#include "display.h"

Module::Module(const char* name, uint32_t stackSize, UBaseType_t priority)
    : region(nullptr), _name(name), _stackSize(stackSize),
      _priority(priority), _taskHandle(nullptr), _eventQueue(nullptr),
      _running(false), _shouldStop(false), _x(0), _y(0), _width(0), _height(0)
{
    // Create event queue
    _eventQueue = xQueueCreate(EVENT_QUEUE_SIZE, sizeof(ModuleEvent));
}

Module::~Module()
{
    stop();

    if (_eventQueue) {
        vQueueDelete(_eventQueue);
    }

    // Note: region is managed by ModuleManager, not by the Module
}

bool Module::initialize(DisplayRegion* region)
{
    if (!region) {
        Serial.printf("Module %s: DisplayRegion is null\n", _name);
        return false;
    }

    this->region = region;

    // Get geometry directly from the region
    _x = region->getX();
    _y = region->getY();
    _width = region->getWidth();
    _height = region->getHeight();

    Serial.printf("Module %s: Initialized with region (%d,%d,%d,%d)\n",
                  _name, _x, _y, _width, _height);
    return true;
}

bool Module::start()
{
    if (_running) {
        Serial.printf("Module %s: Already running\n", _name);
        return true;
    }
    
    if (!region) {
        Serial.printf("Module %s: Cannot start - not initialized\n", _name);
        return false;
    }
    
    _shouldStop = false;
    
    // Create the task
    BaseType_t result = xTaskCreate(
        taskWrapper,
        _name,
        _stackSize,
        this,
        _priority,
        &_taskHandle
    );
    
    if (result != pdPASS) {
        Serial.printf("Module %s: Failed to create task\n", _name);
        return false;
    }
    
    _running = true;
    Serial.printf("Module %s: Started successfully\n", _name);
    return true;
}

void Module::stop()
{
    if (!_running) {
        return;
    }
    
    Serial.printf("Module %s: Stopping...\n", _name);
    _shouldStop = true;
    
    // Wait for task to finish
    if (_taskHandle) {
        // Send a stop event to wake up the task if it's waiting
        ModuleEvent stopEvent(ModuleEventType::CUSTOM);
        xQueueSend(_eventQueue, &stopEvent, 0);
        
        // Wait for task to finish (with timeout)
        uint32_t timeout = 0;
        while (_running && timeout < 1000) {
            vTaskDelay(pdMS_TO_TICKS(10));
            timeout += 10;
        }
        
        if (_running) {
            Serial.printf("Module %s: Force deleting task\n", _name);
            vTaskDelete(_taskHandle);
        }
        
        _taskHandle = nullptr;
    }
    
    _running = false;
    Serial.printf("Module %s: Stopped\n", _name);
}

bool Module::sendEvent(const ModuleEvent& event)
{
    if (!_running || !_eventQueue) {
        return false;
    }
    
    BaseType_t result = xQueueSend(_eventQueue, &event, 0);
    return result == pdPASS;
}

bool Module::containsPoint(int32_t x, int32_t y) const
{
    return (x >= _x && x < _x + _width && 
            y >= _y && y < _y + _height);
}

void Module::taskWrapper(void* parameter)
{
    Module* module = static_cast<Module*>(parameter);
    module->taskFunction();
}

void Module::taskFunction()
{
    Serial.printf("Module %s: Task started\n", _name);
    
    // Call setup
    setup();
    
    // Main loop
    while (!_shouldStop) {
        // Check for events
        ModuleEvent event;
        if (xQueueReceive(_eventQueue, &event, EVENT_WAIT_TICKS) == pdPASS) {
            handleEvent(event);
        }
        
        // Call module loop
        loop();
        
        // Small delay to prevent watchdog issues
        vTaskDelay(pdMS_TO_TICKS(1));
    }
    
    // Call cleanup
    cleanup();
    
    _running = false;
    Serial.printf("Module %s: Task finished\n", _name);
    
    // Delete self
    vTaskDelete(nullptr);
}
