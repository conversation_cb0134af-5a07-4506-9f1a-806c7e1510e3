#include "color-module.h"

ColorModule::ColorModule(const char *name, uint16_t color, uint16_t textColor)
    : Module(name, 2048, 1), _backgroundColor(color), _textColor(textColor),
      _lastTouchTime(0), _touched(false)
{
}

ColorModule::~ColorModule()
{
}

void ColorModule::setup()
{
    if (region)
    {
        draw();
    }
}

void ColorModule::loop()
{
    if (!region)
    {
        return;
    }

    // Check if we need to clear the touch indicator
    if (_touched && (millis() - _lastTouchTime > 1000))
    {
        _touched = false;
        draw(); // Redraw without touch indicator
    }

    // Small delay to prevent excessive CPU usage
    vTaskDelay(pdMS_TO_TICKS(50));
}

void ColorModule::handleEvent(const ModuleEvent &event)
{
    switch (event.type)
    {
    case ModuleEventType::TOUCH_PRESS:
    case ModuleEventType::TOUCH_MOVE:
        handleTouch(event);
        break;
    case ModuleEventType::BUTTON_PRESS:
        Serial.printf("ColorModule %s: Button %d pressed\n", getName(), event.buttonId);
        break;
    default:
        // Ignore other events
        break;
    }
}

void ColorModule::draw()
{
    if (!region)
        return;

    region->clear(_backgroundColor);

    region->setCursor(5, 5);
    region->setTextColor(_textColor);
    region->setTextSize(1);
    // region->print(getName());
    region->printf("%dx%d", region->getX(), region->getY());

    if (_touched)
    {
        int centerX = region->getWidth() / 2;
        int centerY = region->getHeight() / 2;
        region->fillCircle(centerX, centerY, 20, TFT_YELLOW);

        region->setCursor(centerX - 30, centerY + 30);
        region->setTextColor(TFT_BLACK);
        region->setTextSize(1);
        region->print("TOUCHED!");
    }

    region->setCursor(5, region->getHeight() - 10);
    region->setTextColor(_textColor);
    region->setTextSize(1);
    region->printf("%dx%d", region->getWidth(), region->getHeight());
}

void ColorModule::handleTouch(const ModuleEvent &event)
{
    _touched = true;
    _lastTouchTime = millis();

    Serial.printf("ColorModule %s: Touch at (%d,%d)\n", getName(), event.x, event.y);

    draw();
}
