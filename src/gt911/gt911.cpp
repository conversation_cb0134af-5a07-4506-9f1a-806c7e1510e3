#include <Wire.h>

#include "gt911/gt911.h"

void GT911::begin(int sda, int scl) {
    Wire.begin(sda, scl);
}

TouchPoint GT911::getTouchPoint() {
    return readTouchData();
}

TouchPoint GT911::readTouchData() {
    TouchPoint point = {false, 0, 0};

    Wire.beginTransmission(GT911_ADDR);
    Wire.write(GT911_REG_STATUS >> 8);
    Wire.write(GT911_REG_STATUS & 0xFF);
    if (Wire.endTransmission() != 0)
        return point;

    Wire.requestFrom(GT911_ADDR, 1);
    if (Wire.available()) {
        uint8_t status = Wire.read();
        if ((status & 0x80) && (status & 0x0F)) {
            point.touched = true;

            Wire.beginTransmission(GT911_ADDR);
            Wire.write(GT911_POINT1_REG >> 8);
            Wire.write(GT911_POINT1_REG & 0xFF);
            Wire.endTransmission();

            Wire.requestFrom(GT911_ADDR, 4);
            if (Wire.available() >= 4) {
                point.x = Wire.read() | (Wire.read() << 8);
                point.y = Wire.read() | (Wire.read() << 8);
            }
        }

        // Clear the status register
        Wire.beginTransmission(GT911_ADDR);
        Wire.write((uint8_t)(GT911_REG_STATUS >> 8));
        Wire.write((uint8_t)(GT911_REG_STATUS & 0xFF));
        Wire.write(0x00);
        Wire.endTransmission();
    }

    return point;
}