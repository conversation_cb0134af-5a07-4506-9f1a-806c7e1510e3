#include <Arduino.h>
#include <WiFi.h>

#include "display.h"
#include "module-manager.h"
#include "system-ui.h"
#include "color-module.h"
#include "input.h"
#include "config.h"
#include "wifi-handler.h"

Display *display;
ModuleManager *moduleManager;
SystemUI *systemUI;
ColorModule *redModule;
ColorModule *greenModule;
ColorModule *blueModule;
ColorModule *whiteModule;
Input *input;
Config *config;
WifiHandler *wifi;

void setupModules()
{
  // Create module manager
  moduleManager = new ModuleManager(display);

  // Calculate the available screen area (excluding SystemUI at top)
  int systemUIHeight = 30;
  int availableWidth = display->getWidth();
  int availableHeight = display->getHeight() - systemUIHeight;
  int startY = systemUIHeight;

  // Calculate quadrant dimensions
  int quadrantWidth = availableWidth / 2;
  int quadrantHeight = availableHeight / 2;

  // Create SystemUI module
  systemUI = new SystemUI();
  if (!moduleManager->registerModule(systemUI, 0, 0, availableWidth, systemUIHeight))
  {
    Serial.println("ERROR: Failed to register SystemUI module");
    return;
  }

  // Create color modules for each quadrant
  redModule = new ColorModule("RED", TFT_RED, TFT_WHITE);
  if (!moduleManager->registerModule(redModule, 0, startY, quadrantWidth, quadrantHeight))
  {
    Serial.println("ERROR: Failed to register RED module");
    return;
  }

  greenModule = new ColorModule("GREEN", TFT_GREEN, TFT_BLACK);
  if (!moduleManager->registerModule(greenModule, quadrantWidth, startY, quadrantWidth, quadrantHeight))
  {
    Serial.println("ERROR: Failed to register GREEN module");
    return;
  }

  blueModule = new ColorModule("BLUE", TFT_BLUE, TFT_WHITE);
  if (!moduleManager->registerModule(blueModule, 0, startY + quadrantHeight, quadrantWidth, quadrantHeight))
  {
    Serial.println("ERROR: Failed to register BLUE module");
    return;
  }

  whiteModule = new ColorModule("WHITE", TFT_WHITE, TFT_BLACK);
  if (!moduleManager->registerModule(whiteModule, quadrantWidth, startY + quadrantHeight, quadrantWidth, quadrantHeight))
  {
    Serial.println("ERROR: Failed to register WHITE module");
    return;
  }

  // Start all modules
  if (moduleManager->startAllModules())
  {
    Serial.println("All modules started successfully");
  }
  else
  {
    Serial.println("ERROR: Some modules failed to start");
  }

  moduleManager->printStatus();
}

void setup()
{
  Serial.begin(115200);
  delay(2000);

  display = new Display();
  display->setup();

  // Setup all modules
  setupModules();

  input = new Input();
  input->setup();

  config = new Config();
  const char *ssid = config->getWifiSsid();
  const char *password = config->getWifiPassword();

  if (ssid != NULL && password != NULL)
  {
    wifi = new WifiHandler();
    wifi->setup(ssid, password);
  }
}

void loop()
{
  input->loop();

  // Modules run in their own threads, so we don't need to call their loop() methods here
  // Just handle any global tasks or events

  delay(100); // Small delay to prevent excessive CPU usage
}