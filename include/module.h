#ifndef MODULE_H
#define MODULE_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include "display-region.h"

/**
 * Event types that can be sent to modules
 */
enum class ModuleEventType {
    TOUCH_PRESS,
    TOUCH_RELEASE,
    TOUCH_MOVE,
    BUTTON_PRESS,
    BUTTON_RELEASE,
    TIMER_TICK,
    CUSTOM
};

/**
 * Event data structure
 */
struct ModuleEvent {
    ModuleEventType type;
    int32_t x, y;           // For touch events
    uint8_t buttonId;       // For button events
    uint32_t data;          // For custom data
    void* customData;       // For complex custom data
    
    ModuleEvent(ModuleEventType t = ModuleEventType::CUSTOM, int32_t x = 0, int32_t y = 0, 
                uint8_t btn = 0, uint32_t d = 0, void* custom = nullptr)
        : type(t), x(x), y(y), buttonId(btn), data(d), customData(custom) {}
};

/**
 * Base class for all modules
 * Each module runs in its own thread and has its own display region
 */
class Module {
public:
    Module(const char* name, uint32_t stackSize = 4096, UBaseType_t priority = 1);
    virtual ~Module();
    
    /**
     * Initialize the module with a display region
     * @param region Pointer to the allocated DisplayRegion for this module
     * @return true if initialization successful
     */
    bool initialize(DisplayRegion* region);
    
    /**
     * Start the module's thread
     * @return true if thread started successfully
     */
    bool start();
    
    /**
     * Stop the module's thread
     */
    void stop();
    
    /**
     * Send an event to this module
     * @param event The event to send
     * @return true if event was queued successfully
     */
    bool sendEvent(const ModuleEvent& event);
    
    /**
     * Check if a point is within this module's region
     * @param x X coordinate
     * @param y Y coordinate
     * @return true if point is within the module's region
     */
    bool containsPoint(int32_t x, int32_t y) const;
    
    /**
     * Get module information
     */
    const char* getName() const { return _name; }
    bool isRunning() const { return _running; }
    int32_t getX() const { return _x; }
    int32_t getY() const { return _y; }
    int32_t getWidth() const { return _width; }
    int32_t getHeight() const { return _height; }

protected:
    /**
     * Module setup - called once when module starts
     * Override this to perform module-specific initialization
     */
    virtual void setup() = 0;
    
    /**
     * Module main loop - called repeatedly while module is running
     * Override this to implement module behavior
     */
    virtual void loop() = 0;
    
    /**
     * Handle events sent to this module
     * Override this to handle touch, button, and custom events
     * @param event The event to handle
     */
    virtual void handleEvent(const ModuleEvent& event) {}
    
    /**
     * Module cleanup - called when module is stopping
     * Override this to perform cleanup
     */
    virtual void cleanup() {}
    
    // Protected members accessible to derived classes
    DisplayRegion* region;      // The module's display region
    
private:
    // Static wrapper for FreeRTOS task
    static void taskWrapper(void* parameter);
    
    // Main task function
    void taskFunction();
    
    // Module properties
    const char* _name;
    uint32_t _stackSize;
    UBaseType_t _priority;
    
    // Thread management
    TaskHandle_t _taskHandle;
    QueueHandle_t _eventQueue;
    bool _running;
    bool _shouldStop;
    
    // Region properties
    int32_t _x, _y, _width, _height;
    
    // Constants
    static const uint8_t EVENT_QUEUE_SIZE = 10;
    static const TickType_t EVENT_WAIT_TICKS = pdMS_TO_TICKS(100);
};

#endif // MODULE_H
